/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import type { CreateFileRoute, FileRoutesByPath } from '@tanstack/react-router'

import { Route as rootRouteImport } from './routes/__root'
import { Route as GuestRouteImport } from './routes/_guest'
import { Route as AuthRouteImport } from './routes/_auth'
import { Route as AuthIndexRouteImport } from './routes/_auth/index'
import { Route as GuestRegisterRouteImport } from './routes/_guest/register'
import { Route as GuestLoginRouteImport } from './routes/_guest/login'
import { Route as AuthTransactionsIndexRouteImport } from './routes/_auth/transactions.index'
import { Route as AuthCategoriesIndexRouteImport } from './routes/_auth/categories.index'
import { Route as AuthBudgetsIndexRouteImport } from './routes/_auth/budgets.index'
import { Route as AuthAccountsIndexRouteImport } from './routes/_auth/accounts.index'
import { Route as AuthAccountsAccountIdRouteImport } from './routes/_auth/accounts.$accountId'

const GuestRoute = GuestRouteImport.update({
  id: '/_guest',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthRoute = AuthRouteImport.update({
  id: '/_auth',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthIndexRoute = AuthIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthRoute,
} as any)
const GuestRegisterRoute = GuestRegisterRouteImport.update({
  id: '/register',
  path: '/register',
  getParentRoute: () => GuestRoute,
} as any)
const GuestLoginRoute = GuestLoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => GuestRoute,
} as any)
const AuthTransactionsIndexRoute = AuthTransactionsIndexRouteImport.update({
  id: '/transactions/',
  path: '/transactions/',
  getParentRoute: () => AuthRoute,
} as any)
const AuthCategoriesIndexRoute = AuthCategoriesIndexRouteImport.update({
  id: '/categories/',
  path: '/categories/',
  getParentRoute: () => AuthRoute,
} as any)
const AuthBudgetsIndexRoute = AuthBudgetsIndexRouteImport.update({
  id: '/budgets/',
  path: '/budgets/',
  getParentRoute: () => AuthRoute,
} as any)
const AuthAccountsIndexRoute = AuthAccountsIndexRouteImport.update({
  id: '/accounts/',
  path: '/accounts/',
  getParentRoute: () => AuthRoute,
} as any)
const AuthAccountsAccountIdRoute = AuthAccountsAccountIdRouteImport.update({
  id: '/accounts/$accountId',
  path: '/accounts/$accountId',
  getParentRoute: () => AuthRoute,
} as any)

export interface FileRoutesByFullPath {
  '/login': typeof GuestLoginRoute
  '/register': typeof GuestRegisterRoute
  '/': typeof AuthIndexRoute
  '/accounts/$accountId': typeof AuthAccountsAccountIdRoute
  '/accounts': typeof AuthAccountsIndexRoute
  '/budgets': typeof AuthBudgetsIndexRoute
  '/categories': typeof AuthCategoriesIndexRoute
  '/transactions': typeof AuthTransactionsIndexRoute
}
export interface FileRoutesByTo {
  '/login': typeof GuestLoginRoute
  '/register': typeof GuestRegisterRoute
  '/': typeof AuthIndexRoute
  '/accounts/$accountId': typeof AuthAccountsAccountIdRoute
  '/accounts': typeof AuthAccountsIndexRoute
  '/budgets': typeof AuthBudgetsIndexRoute
  '/categories': typeof AuthCategoriesIndexRoute
  '/transactions': typeof AuthTransactionsIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_auth': typeof AuthRouteWithChildren
  '/_guest': typeof GuestRouteWithChildren
  '/_guest/login': typeof GuestLoginRoute
  '/_guest/register': typeof GuestRegisterRoute
  '/_auth/': typeof AuthIndexRoute
  '/_auth/accounts/$accountId': typeof AuthAccountsAccountIdRoute
  '/_auth/accounts/': typeof AuthAccountsIndexRoute
  '/_auth/budgets/': typeof AuthBudgetsIndexRoute
  '/_auth/categories/': typeof AuthCategoriesIndexRoute
  '/_auth/transactions/': typeof AuthTransactionsIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/login'
    | '/register'
    | '/'
    | '/accounts/$accountId'
    | '/accounts'
    | '/budgets'
    | '/categories'
    | '/transactions'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/login'
    | '/register'
    | '/'
    | '/accounts/$accountId'
    | '/accounts'
    | '/budgets'
    | '/categories'
    | '/transactions'
  id:
    | '__root__'
    | '/_auth'
    | '/_guest'
    | '/_guest/login'
    | '/_guest/register'
    | '/_auth/'
    | '/_auth/accounts/$accountId'
    | '/_auth/accounts/'
    | '/_auth/budgets/'
    | '/_auth/categories/'
    | '/_auth/transactions/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  AuthRoute: typeof AuthRouteWithChildren
  GuestRoute: typeof GuestRouteWithChildren
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_auth': {
      id: '/_auth'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_guest': {
      id: '/_guest'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof GuestRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_guest/login': {
      id: '/_guest/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof GuestLoginRouteImport
      parentRoute: typeof GuestRoute
    }
    '/_guest/register': {
      id: '/_guest/register'
      path: '/register'
      fullPath: '/register'
      preLoaderRoute: typeof GuestRegisterRouteImport
      parentRoute: typeof GuestRoute
    }
    '/_auth/': {
      id: '/_auth/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthIndexRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/accounts/$accountId': {
      id: '/_auth/accounts/$accountId'
      path: '/accounts/$accountId'
      fullPath: '/accounts/$accountId'
      preLoaderRoute: typeof AuthAccountsAccountIdRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/accounts/': {
      id: '/_auth/accounts/'
      path: '/accounts'
      fullPath: '/accounts'
      preLoaderRoute: typeof AuthAccountsIndexRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/budgets/': {
      id: '/_auth/budgets/'
      path: '/budgets'
      fullPath: '/budgets'
      preLoaderRoute: typeof AuthBudgetsIndexRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/categories/': {
      id: '/_auth/categories/'
      path: '/categories'
      fullPath: '/categories'
      preLoaderRoute: typeof AuthCategoriesIndexRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/transactions/': {
      id: '/_auth/transactions/'
      path: '/transactions'
      fullPath: '/transactions'
      preLoaderRoute: typeof AuthTransactionsIndexRouteImport
      parentRoute: typeof AuthRoute
    }
  }
}

declare module './routes/_auth' {
  const createFileRoute: CreateFileRoute<
    '/_auth',
    FileRoutesByPath['/_auth']['parentRoute'],
    FileRoutesByPath['/_auth']['id'],
    FileRoutesByPath['/_auth']['path'],
    FileRoutesByPath['/_auth']['fullPath']
  >
}
declare module './routes/_guest' {
  const createFileRoute: CreateFileRoute<
    '/_guest',
    FileRoutesByPath['/_guest']['parentRoute'],
    FileRoutesByPath['/_guest']['id'],
    FileRoutesByPath['/_guest']['path'],
    FileRoutesByPath['/_guest']['fullPath']
  >
}
declare module './routes/_guest/login' {
  const createFileRoute: CreateFileRoute<
    '/_guest/login',
    FileRoutesByPath['/_guest/login']['parentRoute'],
    FileRoutesByPath['/_guest/login']['id'],
    FileRoutesByPath['/_guest/login']['path'],
    FileRoutesByPath['/_guest/login']['fullPath']
  >
}
declare module './routes/_guest/register' {
  const createFileRoute: CreateFileRoute<
    '/_guest/register',
    FileRoutesByPath['/_guest/register']['parentRoute'],
    FileRoutesByPath['/_guest/register']['id'],
    FileRoutesByPath['/_guest/register']['path'],
    FileRoutesByPath['/_guest/register']['fullPath']
  >
}
declare module './routes/_auth/index' {
  const createFileRoute: CreateFileRoute<
    '/_auth/',
    FileRoutesByPath['/_auth/']['parentRoute'],
    FileRoutesByPath['/_auth/']['id'],
    FileRoutesByPath['/_auth/']['path'],
    FileRoutesByPath['/_auth/']['fullPath']
  >
}
declare module './routes/_auth/accounts.$accountId' {
  const createFileRoute: CreateFileRoute<
    '/_auth/accounts/$accountId',
    FileRoutesByPath['/_auth/accounts/$accountId']['parentRoute'],
    FileRoutesByPath['/_auth/accounts/$accountId']['id'],
    FileRoutesByPath['/_auth/accounts/$accountId']['path'],
    FileRoutesByPath['/_auth/accounts/$accountId']['fullPath']
  >
}
declare module './routes/_auth/accounts.index' {
  const createFileRoute: CreateFileRoute<
    '/_auth/accounts/',
    FileRoutesByPath['/_auth/accounts/']['parentRoute'],
    FileRoutesByPath['/_auth/accounts/']['id'],
    FileRoutesByPath['/_auth/accounts/']['path'],
    FileRoutesByPath['/_auth/accounts/']['fullPath']
  >
}
declare module './routes/_auth/budgets.index' {
  const createFileRoute: CreateFileRoute<
    '/_auth/budgets/',
    FileRoutesByPath['/_auth/budgets/']['parentRoute'],
    FileRoutesByPath['/_auth/budgets/']['id'],
    FileRoutesByPath['/_auth/budgets/']['path'],
    FileRoutesByPath['/_auth/budgets/']['fullPath']
  >
}
declare module './routes/_auth/categories.index' {
  const createFileRoute: CreateFileRoute<
    '/_auth/categories/',
    FileRoutesByPath['/_auth/categories/']['parentRoute'],
    FileRoutesByPath['/_auth/categories/']['id'],
    FileRoutesByPath['/_auth/categories/']['path'],
    FileRoutesByPath['/_auth/categories/']['fullPath']
  >
}
declare module './routes/_auth/transactions.index' {
  const createFileRoute: CreateFileRoute<
    '/_auth/transactions/',
    FileRoutesByPath['/_auth/transactions/']['parentRoute'],
    FileRoutesByPath['/_auth/transactions/']['id'],
    FileRoutesByPath['/_auth/transactions/']['path'],
    FileRoutesByPath['/_auth/transactions/']['fullPath']
  >
}

interface AuthRouteChildren {
  AuthIndexRoute: typeof AuthIndexRoute
  AuthAccountsAccountIdRoute: typeof AuthAccountsAccountIdRoute
  AuthAccountsIndexRoute: typeof AuthAccountsIndexRoute
  AuthBudgetsIndexRoute: typeof AuthBudgetsIndexRoute
  AuthCategoriesIndexRoute: typeof AuthCategoriesIndexRoute
  AuthTransactionsIndexRoute: typeof AuthTransactionsIndexRoute
}

const AuthRouteChildren: AuthRouteChildren = {
  AuthIndexRoute: AuthIndexRoute,
  AuthAccountsAccountIdRoute: AuthAccountsAccountIdRoute,
  AuthAccountsIndexRoute: AuthAccountsIndexRoute,
  AuthBudgetsIndexRoute: AuthBudgetsIndexRoute,
  AuthCategoriesIndexRoute: AuthCategoriesIndexRoute,
  AuthTransactionsIndexRoute: AuthTransactionsIndexRoute,
}

const AuthRouteWithChildren = AuthRoute._addFileChildren(AuthRouteChildren)

interface GuestRouteChildren {
  GuestLoginRoute: typeof GuestLoginRoute
  GuestRegisterRoute: typeof GuestRegisterRoute
}

const GuestRouteChildren: GuestRouteChildren = {
  GuestLoginRoute: GuestLoginRoute,
  GuestRegisterRoute: GuestRegisterRoute,
}

const GuestRouteWithChildren = GuestRoute._addFileChildren(GuestRouteChildren)

const rootRouteChildren: RootRouteChildren = {
  AuthRoute: AuthRouteWithChildren,
  GuestRoute: GuestRouteWithChildren,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
