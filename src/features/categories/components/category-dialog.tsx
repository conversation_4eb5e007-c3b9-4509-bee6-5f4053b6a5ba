import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "~/components/ui/dialog";

import { useCategoryActions } from "../hooks";
import { useCategoryActionsStore } from "../store";
import CategoryFormCreate from "./category-form-create";
import CategoryFormUpdate from "./category-form-update";

export default function CategoryDialog() {
  // Store state and actions
  const { categoryDialog } = useCategoryActionsStore();
  const { closeCategoryDialog } = useCategoryActions();

  const isEditing = !!categoryDialog.editingCategory;

  return (
    <Dialog open={categoryDialog.isOpen} onOpenChange={closeCategoryDialog}>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Edit Category" : "Create Category"}</DialogTitle>
          <DialogDescription>
            {isEditing 
              ? "Update the category details below." 
              : "Create a new category to organize your transactions."}
          </DialogDescription>
        </DialogHeader>

        {isEditing ? (
          <CategoryFormUpdate category={categoryDialog.editingCategory!} />
        ) : (
          <CategoryFormCreate />
        )}
      </DialogContent>
    </Dialog>
  );
}
