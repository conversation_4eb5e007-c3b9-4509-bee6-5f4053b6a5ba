import type { Category, CategoriesGroupedData } from "../types";

import { useState } from "react";

import { PlusIcon, SearchIcon } from "lucide-react";

import { LoadingIndicator } from "~/components/elements";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";

import { filterCategories } from "../utils";
import { useCategoryActions } from "../hooks";
import CategoryCard from "./category-card";

interface Props {
  categories: Category[];
  groupedCategories: CategoriesGroupedData;
  isLoading: boolean;
}

export default function CategoriesList({ categories, groupedCategories, isLoading }: Props) {
  const [searchQuery, setSearchQuery] = useState("");
  const { createCategory } = useCategoryActions();

  // Filter categories based on search query
  const filteredExpenseCategories = filterCategories(groupedCategories.expense, searchQuery);
  const filteredIncomeCategories = filterCategories(groupedCategories.income, searchQuery);
  const hasResults = filteredExpenseCategories.length > 0 || filteredIncomeCategories.length > 0;

  if (isLoading) {
    return <LoadingIndicator />;
  }

  return (
    <div className="space-y-6">
      {/* Header with search and add button */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="relative flex-1 max-w-md">
          <SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search categories..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>
        <Button onClick={createCategory}>
          <PlusIcon className="mr-2 h-4 w-4" />
          Add Category
        </Button>
      </div>

      {/* Categories display */}
      {categories.length === 0 ? (
        <div className="py-12 text-center">
          <p className="text-muted-foreground mb-4">No categories found</p>
          <Button onClick={createCategory}>
            <PlusIcon className="mr-2 h-4 w-4" />
            Create your first category
          </Button>
        </div>
      ) : !hasResults && searchQuery ? (
        <div className="py-12 text-center">
          <p className="text-muted-foreground">No categories match your search</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
          {/* Expense Categories */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <span className="text-2xl">💸</span>
              <h2 className="text-xl font-semibold">Expense Categories</h2>
              <span className="text-muted-foreground text-sm">
                ({filteredExpenseCategories.length})
              </span>
            </div>

            {filteredExpenseCategories.length === 0 ? (
              <div className="py-8 text-center text-muted-foreground">
                {searchQuery ? "No expense categories match your search" : "No expense categories yet"}
              </div>
            ) : (
              <div className="space-y-3">
                {filteredExpenseCategories.map((category) => (
                  <CategoryCard key={category.id} category={category} />
                ))}
              </div>
            )}
          </div>

          {/* Income Categories */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <span className="text-2xl">💰</span>
              <h2 className="text-xl font-semibold">Income Categories</h2>
              <span className="text-muted-foreground text-sm">
                ({filteredIncomeCategories.length})
              </span>
            </div>

            {filteredIncomeCategories.length === 0 ? (
              <div className="py-8 text-center text-muted-foreground">
                {searchQuery ? "No income categories match your search" : "No income categories yet"}
              </div>
            ) : (
              <div className="space-y-3">
                {filteredIncomeCategories.map((category) => (
                  <CategoryCard key={category.id} category={category} />
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
