import type { Category } from "../types";

import { <PERSON> } from "@tanstack/react-router";
import { ExternalLinkIcon } from "lucide-react";

import { Box } from "~/components/blocks";
import { Button } from "~/components/ui/button";

interface Props {
  category: Category;
  className?: string;
}

export default function CategoryDetailsTransactions({ category, className }: Props) {
  return (
    <Box className={className}>
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">Related Transactions</h2>
        <Button variant="outline" size="sm" asChild>
          <Link to="/transactions" search={{ categoryId: category.id }}>
            <ExternalLinkIcon className="mr-2 h-4 w-4" />
            View All
          </Link>
        </Button>
      </div>

      {/* Placeholder for transactions list */}
      <div className="py-8 text-center text-muted-foreground">
        <p>Transaction integration will be implemented in a future phase.</p>
        <p className="text-sm mt-2">
          This section will show recent transactions using this category.
        </p>
      </div>
    </Box>
  );
}
