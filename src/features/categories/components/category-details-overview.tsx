import type { Category } from "../types";

import { EditIcon, TrashIcon } from "lucide-react";

import { Box } from "~/components/blocks";
import { Button } from "~/components/ui/button";

import { getCategoryTypeIcon, getCategoryTypeLabel } from "../utils";
import { useCategoryActions } from "../hooks";

interface Props {
  category: Category;
}

export default function CategoryDetailsOverview({ category }: Props) {
  const { editCategory, deleteCategory } = useCategoryActions();

  const handleEdit = () => {
    editCategory(category);
  };

  const handleDelete = () => {
    deleteCategory(category);
  };

  return (
    <Box>
      <div className="flex items-start justify-between">
        <div className="flex items-start gap-4">
          {/* Color indicator */}
          {category.color && (
            <div
              className="w-6 h-6 rounded-full flex-shrink-0 mt-1"
              style={{ backgroundColor: category.color }}
            />
          )}

          <div className="space-y-3">
            {/* Category name and icon */}
            <div className="flex items-center gap-3">
              <span className="text-3xl">
                {category.icon || getCategoryTypeIcon(category.isExpense)}
              </span>
              <div>
                <h1 className="text-2xl font-bold text-foreground">{category.name}</h1>
                <div className="flex items-center gap-2 mt-1">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    category.isExpense 
                      ? "bg-red-100 text-red-800" 
                      : "bg-green-100 text-green-800"
                  }`}>
                    {getCategoryTypeLabel(category.isExpense)}
                  </span>
                </div>
              </div>
            </div>

            {/* Category metadata */}
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              <div>
                <dt className="text-sm font-medium text-muted-foreground">Type</dt>
                <dd className="text-sm text-foreground">
                  {getCategoryTypeLabel(category.isExpense)}
                </dd>
              </div>

              {category.color && (
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">Color</dt>
                  <dd className="flex items-center gap-2 text-sm text-foreground">
                    <div
                      className="w-4 h-4 rounded border"
                      style={{ backgroundColor: category.color }}
                    />
                    {category.color}
                  </dd>
                </div>
              )}

              <div>
                <dt className="text-sm font-medium text-muted-foreground">Created</dt>
                <dd className="text-sm text-foreground">
                  {new Date(category.createdAt).toLocaleDateString()}
                </dd>
              </div>

              {category.updatedAt !== category.createdAt && (
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">Last Updated</dt>
                  <dd className="text-sm text-foreground">
                    {new Date(category.updatedAt).toLocaleDateString()}
                  </dd>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleEdit}>
            <EditIcon className="mr-2 h-4 w-4" />
            Edit
          </Button>
          <Button variant="outline" size="sm" onClick={handleDelete}>
            <TrashIcon className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>
    </Box>
  );
}
