import type { Category } from "../types";

import { Link } from "@tanstack/react-router";
import { EditIcon, TrashIcon } from "lucide-react";

import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";

import { getCategoryTypeIcon } from "../utils";
import { useCategoryActions } from "../hooks";

interface Props {
  category: Category;
}

export default function CategoryCard({ category }: Props) {
  const { editCategory, deleteCategory } = useCategoryActions();

  const handleEdit = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    editCategory(category);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    deleteCategory(category);
  };

  return (
    <Card className="group hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <Link
          to="/categories/$categoryId"
          params={{ categoryId: category.id }}
          className="block"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3 min-w-0 flex-1">
              {/* Color indicator */}
              {category.color && (
                <div
                  className="w-3 h-3 rounded-full flex-shrink-0"
                  style={{ backgroundColor: category.color }}
                />
              )}

              {/* Category info */}
              <div className="min-w-0 flex-1">
                <div className="flex items-center gap-2">
                  {/* Icon */}
                  <span className="text-lg flex-shrink-0">
                    {category.icon || getCategoryTypeIcon(category.isExpense)}
                  </span>

                  {/* Name */}
                  <h3 className="font-medium text-foreground truncate">
                    {category.name}
                  </h3>
                </div>

                {/* Type badge */}
                <div className="flex items-center gap-1 mt-1">
                  <span className="text-xs text-muted-foreground">
                    {category.isExpense ? "Expense" : "Income"}
                  </span>
                </div>
              </div>
            </div>

            {/* Action buttons */}
            <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleEdit}
                className="h-8 w-8 p-0"
              >
                <EditIcon className="h-4 w-4" />
                <span className="sr-only">Edit category</span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDelete}
                className="h-8 w-8 p-0 text-destructive hover:text-destructive"
              >
                <TrashIcon className="h-4 w-4" />
                <span className="sr-only">Delete category</span>
              </Button>
            </div>
          </div>
        </Link>
      </CardContent>
    </Card>
  );
}
