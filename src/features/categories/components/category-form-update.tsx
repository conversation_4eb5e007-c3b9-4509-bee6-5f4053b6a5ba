import type { CategoryUpdateRequest } from "~/api/types.gen";
import type { Category } from "../types";

import { useEffect, useMemo } from "react";
import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";

import { zCategoryUpdateRequest } from "~/api/zod.gen";
import { InputColor, InputText } from "~/components/inputs";
import { Button } from "~/components/ui/button";
import { DialogFooter } from "~/components/ui/dialog";
import { Form } from "~/components/ui/form";

import { useCategoryActions, useUpdateCategory } from "../hooks";

interface Props {
  category: Category;
}

export default function CategoryFormUpdate(props: Props) {
  const { category } = props;

  const { updateCategoryMutation, closeCategoryDialog } = useCategoryActions();

  const defaultValues = useMemo<CategoryUpdateRequest>(
    () => ({
      name: category.name,
      color: category.color || "",
      icon: category.icon || "",
      // Note: isExpense is not editable after creation as per requirements
    }),
    [category]
  );

  const form = useForm<CategoryUpdateRequest>({
    resolver: zodResolver(zCategoryUpdateRequest),
    defaultValues,
  });

  const resetForm = form.reset;

  // Reset form when category changes
  useEffect(() => {
    resetForm(defaultValues);
  }, [defaultValues, resetForm]);

  const handleSubmit = form.handleSubmit((data: CategoryUpdateRequest) => {
    // Convert empty strings to null/undefined
    const processedData = {
      ...data,
      color: data.color?.trim() || undefined,
      icon: data.icon?.trim() || undefined,
    };

    updateCategoryMutation(category.id, processedData);
  });

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <InputText
            control={form.control}
            name="name"
            label="Category Name"
            placeholder="e.g., Groceries, Salary"
          />

          <div className="flex flex-col gap-1">
            <label className="text-xs/5 font-semibold text-gray-500 uppercase">Category Type</label>
            <div className="flex items-center gap-2 rounded-md border border-input bg-background px-3 py-2 text-sm">
              <span className="text-lg">{category.isExpense ? "💸" : "💰"}</span>
              <span>{category.isExpense ? "Expense" : "Income"}</span>
              <span className="text-muted-foreground text-xs">(Cannot be changed)</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <InputColor
            control={form.control}
            name="color"
            label="Color"
            hint="Optional"
            description="Choose a color to help identify this category"
          />

          <InputText
            control={form.control}
            name="icon"
            label="Icon"
            hint="Optional"
            placeholder="e.g., 🍔, 💰, 🏠"
            description="Add an emoji icon for visual identification"
          />
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={closeCategoryDialog}>
            Cancel
          </Button>
          <Button type="submit" disabled={updateCategoryMutation.isLoading}>
            {updateCategoryMutation.isLoading ? (
              <>
                <LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
                Updating...
              </>
            ) : (
              "Update Category"
            )}
          </Button>
        </DialogFooter>
      </form>
    </Form>
  );
}
