import type { CategoryCreateRequest } from "~/api/types.gen";

import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";

import { zCategoryCreateRequest } from "~/api/zod.gen";
import { InputColor, InputSelect, InputText } from "~/components/inputs";
import { Button } from "~/components/ui/button";
import { DialogFooter } from "~/components/ui/dialog";
import { Form } from "~/components/ui/form";

import { categoryTypeOptions, DEFAULT_CATEGORY_COLOR } from "../constants";
import { useCategoryActions, useCreateCategory } from "../hooks";

export default function CategoryFormCreate() {
  const { createCategoryMutation, closeCategoryDialog } = useCategoryActions();
  const { isLoading } = useCreateCategory();

  const form = useForm<CategoryCreateRequest>({
    resolver: zodResolver(zCategoryCreateRequest),
    defaultValues: {
      name: "",
      color: DEFAULT_CATEGORY_COLOR,
      icon: "",
      isExpense: true,
    },
  });

  const handleSubmit = form.handleSubmit((data: CategoryCreateRequest) => {
    // Convert empty strings to null/undefined
    const processedData = {
      ...data,
      color: data.color?.trim() || undefined,
      icon: data.icon?.trim() || undefined,
    };

    createCategoryMutation(processedData);
  });

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <InputText control={form.control} name="name" label="Category Name" placeholder="e.g., Groceries, Salary" />

          <InputSelect
            values={categoryTypeOptions}
            control={form.control}
            name="isExpense"
            label="Category Type"
            description="Choose whether this is for expenses or income"
          />
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <InputColor
            control={form.control}
            name="color"
            label="Color"
            hint="Optional"
            description="Choose a color to help identify this category"
          />

          <InputText
            control={form.control}
            name="icon"
            label="Icon"
            hint="Optional"
            placeholder="e.g., 🍔, 💰, 🏠"
            description="Add an emoji icon for visual identification"
          />
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={closeCategoryDialog}>
            Cancel
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? (
              <>
                <LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
                Creating...
              </>
            ) : (
              "Create Category"
            )}
          </Button>
        </DialogFooter>
      </form>
    </Form>
  );
}
