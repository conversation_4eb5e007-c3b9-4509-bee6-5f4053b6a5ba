import type { Account } from "~/api/types.gen";

import { Link } from "@tanstack/react-router";
import { Edit2Icon, MoreHorizontalIcon, Trash2Icon } from "lucide-react";

import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { formatCurrency } from "~/lib/formatters";

import { useAccountActions } from "../hooks";
import { getAccountTypeIcon, getAccountTypeLabel } from "../utils";

interface Prop {
  account: Account;
}

export default function AccountRow({ account }: Prop) {
  const Icon = getAccountTypeIcon(account.type);
  const { editAccount, deleteAccount } = useAccountActions();

  return (
    <div className="flex items-center gap-4 rounded-lg bg-white py-4 ps-6 pe-2">
      <div className="flex flex-grow items-center gap-4">
        <p className="text-muted-foreground">
          <Icon className="size-4" />
        </p>

        <Link
          to="/accounts/$accountId"
          params={{ accountId: account.id }}
          className="text-link hover:text-link/90 block text-sm/5 hover:underline"
        >
          {account.name}
        </Link>
      </div>
      <p className="text-foreground w-16 text-sm/5">{getAccountTypeLabel(account.type)}</p>
      <p className="text-foreground w-32 text-right text-sm/5 font-semibold">
        {formatCurrency(account.currency, account.currentBalance)}
      </p>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon" className="place-self-start">
            <MoreHorizontalIcon className="size-4" />
            <span className="sr-only">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => editAccount(account)}>
            <Edit2Icon className="mr-1" />
            Edit
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => deleteAccount(account)} variant="destructive">
            <Trash2Icon className="mr-1" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
